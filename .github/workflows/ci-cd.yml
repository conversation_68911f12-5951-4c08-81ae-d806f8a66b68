name: CI/CD Pipeline
# This workflow uses Neon database for testing
# Required secrets:
# - TEST_DATABASE_URL: Connection string for the Neon test branch

on:
    push:
        branches: [main, production]
    pull_request:
        branches: [main, production]
    workflow_dispatch:
        inputs:
            run_tests:
                description: 'Run tests'
                required: true
                default: true
                type: boolean

permissions:
    contents: read

jobs:
    test-and-build:
        runs-on: ubuntu-latest
        # Only run this job if:
        # 1. It's a push to main or production
        # 2. It's a PR to main or production
        # 3. It's manually triggered with run_tests=true
        if: >
            github.event_name == 'push' ||
            github.event_name == 'pull_request' ||
            (github.event_name == 'workflow_dispatch' && inputs.run_tests)
        # Set default working directory for the job
        defaults:
            run:
                # Only frontend-specific commands will use this working directory
                working-directory: .

        steps:
            - name: Checkout code
              uses: actions/checkout@v4
              with:
                  fetch-depth: 1

            # Set up Node.js with caching - (pointing to root package-lock.json)
            - name: Set up Node.js with caching
              uses: actions/setup-node@v4
              with:
                  node-version: '20.10.0'
                  cache: 'npm'
                  cache-dependency-path: 'package-lock.json'

            - name: Install dependencies
              run: npm ci

            - name: Setup test database
              working-directory: frontend
              run: |
                  # Debug directory structure
                  echo "Current directory: $(pwd)"
                  echo "Directory contents:"
                  ls -la

                  # Run the database setup (migrations on Neon test branch)
                  npx tsx tests/e2e/config/db-setup.ts
              env:
                  DATABASE_URL: ${{ secrets.TEST_DATABASE_URL }}
                  # Auth secrets
                  BETTER_AUTH_SECRET: ${{ secrets.BETTER_AUTH_SECRET }}
                  BETTER_AUTH_URL: ${{ secrets.BETTER_AUTH_URL }}
                  # OAuth providers
                  GOOGLE_CLIENT_ID: ${{ secrets.GOOGLE_CLIENT_ID }}
                  GOOGLE_CLIENT_SECRET: ${{ secrets.GOOGLE_CLIENT_SECRET }}
                  TWITTER_CLIENT_ID: ${{ secrets.TWITTER_CLIENT_ID }}
                  TWITTER_CLIENT_SECRET: ${{ secrets.TWITTER_CLIENT_SECRET }}
                  # Other services
                  RESEND_API_KEY: ${{ secrets.RESEND_API_KEY }}
                  SENTRY_DSN: ${{ secrets.SENTRY_DSN }}

            - name: Run unit tests
              working-directory: frontend
              run: npm run test

            # Cache Playwright browsers
            - name: Cache Playwright browsers
              uses: actions/cache@v4
              id: playwright-cache
              with:
                  path: ~/.cache/ms-playwright
                  key: playwright-${{ runner.os }}-${{ hashFiles('**/package-lock.json') }}
                  restore-keys: |
                      playwright-${{ runner.os }}-

            # Install Playwright browsers (will be skipped if cache hit)
            - name: Install Playwright browsers
              working-directory: frontend
              if: steps.playwright-cache.outputs.cache-hit != 'true'
              run: npx playwright install --with-deps chromium

            - name: Run E2E tests (smoke tests)
              working-directory: frontend
              run: npm run test:e2e:ci

            # Build the application
            - name: Build for production
              working-directory: frontend
              run: npm run build

            # Notify on successful build and test
            - name: Notify on successful build
              if: success() && github.ref == 'refs/heads/production'
              run: |
                  echo "✅ Build and tests passed successfully on production branch!"
                  echo "Coolify will automatically detect this change and deploy the application."

            # Optional: Add a comment to the commit or PR
            - name: Add success comment
              if: success() && github.event_name == 'pull_request'
              run: |
                  echo "✅ All tests passed! This PR is ready to be merged."
                  # You could use the GitHub API to post this as a PR comment if desired
