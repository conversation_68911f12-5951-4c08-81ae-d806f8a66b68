import { test, expect } from '@playwright/test';
import { waitForPageLoad } from './utils/helpers.js';

test.describe('Home Page', () => {
	test('renders home page without errors', async ({ page }) => {
		// Navigate to the home page
		await page.goto('/');

		// Wait for the page to be fully loaded
		await waitForPageLoad(page);

		// Verify the page loaded successfully (smoke test)
		await expect(page).toHaveTitle(/Foundation/);

		// Check that basic page structure is present
		await expect(page.locator('body')).toBeVisible();
	});
});
