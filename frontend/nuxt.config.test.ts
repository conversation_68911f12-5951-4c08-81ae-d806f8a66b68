// Minimal Nuxt configuration for testing
export default defineNuxtConfig({
	// Disable SSR for faster testing
	ssr: false,

	// Minimal modules for testing
	modules: ['@nuxt/ui'],

	// Disable features not needed for smoke tests
	experimental: {
		payloadExtraction: false,
	},

	// Minimal CSS for faster builds
	css: [],

	// Disable analytics and other external services
	runtimeConfig: {
		public: {
			// Minimal runtime config for testing
		},
	},

	// Faster builds for testing
	nitro: {
		minify: false,
		storage: {
			// Disable database storage for tests
			db: { driver: 'memory' },
		},
	},

	// Disable dev tools
	devtools: { enabled: false },

	// Test-specific app config
	app: {
		head: {
			title: 'Foundation Test',
		},
	},
});
