import { test, expect } from '@nuxt/test-utils/playwright';
import { waitForPageLoad } from '../utils/helpers.js';

test.describe('Sign-up Page', () => {
	test('renders sign-up page correctly', async ({ page, goto }) => {
		await goto('/sign-up');
		await waitForPageLoad(page);

		// Check page title and main elements
		await expect(page.getByText('Sign up to Foundation')).toBeVisible();
		await expect(page.getByPlaceholder('Name')).toBeVisible();
		await expect(page.getByPlaceholder('Email address')).toBeVisible();
		await expect(page.getByPlaceholder('Password')).toBeVisible();
		await expect(page.getByRole('button', { name: 'Sign Up with Email' })).toBeVisible();
	});

	test('shows form validation errors for empty fields', async ({ page, goto }) => {
		await goto('/sign-up');
		await waitForPageLoad(page);

		// Try to submit empty form
		await page.getByRole('button', { name: 'Sign Up with Email' }).click();

		// Should stay on sign-up page (form validation prevents submission)
		await expect(page).toHaveURL('/sign-up');
	});

	test('shows form validation errors for invalid data', async ({ page, goto }) => {
		await goto('/sign-up');
		await waitForPageLoad(page);

		await page.getByPlaceholder('Name').fill('A'); // Too short
		await page.getByPlaceholder('Email address').fill('invalid-email'); // Invalid email
		await page.getByPlaceholder('Password').fill('weak'); // Too weak

		await page.getByRole('button', { name: 'Sign Up with Email' }).click();

		// Should stay on sign-up page due to validation
		await expect(page).toHaveURL('/sign-up');
	});

	test('navigates to sign-in page from sign-up page', async ({ page, goto }) => {
		await goto('/sign-up');
		await waitForPageLoad(page);

		await page.getByText('Already have an account?').click();

		await expect(page).toHaveURL('/sign-in');
		await expect(page.getByText('Sign in to Foundation')).toBeVisible();
	});
});
