import { test, expect } from '@nuxt/test-utils/playwright';
import { waitForPageLoad } from '../utils/helpers.js';

test.describe('Forgotten Password Page', () => {
	test('renders forgotten password page correctly', async ({ page, goto }) => {
		await goto('/forgotten-password');
		await waitForPageLoad(page);

		// Check page title and main elements
		await expect(page.getByText('Enter your email below to reset your password')).toBeVisible();
		await expect(page.getByPlaceholder('Email address')).toBeVisible();
		await expect(page.getByRole('button', { name: 'Reset Password' })).toBeVisible();
	});

	test('shows form validation errors for empty email', async ({ page, goto }) => {
		await goto('/forgotten-password');
		await waitForPageLoad(page);

		// Try to submit empty form
		await page.getByRole('button', { name: 'Reset Password' }).click();
		
		// Should stay on forgotten-password page (form validation prevents submission)
		await expect(page).toHaveURL('/forgotten-password');
	});

	test('shows form validation errors for invalid email', async ({ page, goto }) => {
		await goto('/forgotten-password');
		await waitForPageLoad(page);

		await page.getByPlaceholder('Email address').fill('invalid-email');
		await page.getByRole('button', { name: 'Reset Password' }).click();

		// Should stay on forgotten-password page due to validation
		await expect(page).toHaveURL('/forgotten-password');
	});

	test('navigates to sign-in page from forgotten password page', async ({ page, goto }) => {
		await goto('/forgotten-password');
		await waitForPageLoad(page);

		await page.getByText('Back to Sign In').click();

		await expect(page).toHaveURL('/sign-in');
		await expect(page.getByText('Sign in to Foundation')).toBeVisible();
	});
});
