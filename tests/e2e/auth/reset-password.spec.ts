import { test, expect } from '@nuxt/test-utils/playwright';
import { waitForPageLoad } from '../utils/helpers.js';

test.describe('Reset Password Page', () => {
	test('renders reset password page correctly', async ({ page, goto }) => {
		await goto('/reset-password');
		await waitForPageLoad(page);

		// Check page title and main elements
		await expect(page.getByText('Reset your password')).toBeVisible();
		await expect(page.getByPlaceholder('Enter new password')).toBeVisible();
		await expect(page.getByPlaceholder('Confirm new password')).toBeVisible();
		await expect(page.getByRole('button', { name: 'Update Password' })).toBeVisible();
	});

	test('shows form validation errors for empty fields', async ({ page, goto }) => {
		await goto('/reset-password');
		await waitForPageLoad(page);

		// Try to submit empty form
		await page.getByRole('button', { name: 'Update Password' }).click();
		
		// Should stay on reset-password page (form validation prevents submission)
		await expect(page).toHaveURL('/reset-password');
	});

	test('shows form validation errors for weak password', async ({ page, goto }) => {
		await goto('/reset-password');
		await waitForPageLoad(page);

		await page.getByPlaceholder('Enter new password').fill('weak');
		await page.getByPlaceholder('Confirm new password').fill('weak');
		await page.getByRole('button', { name: 'Update Password' }).click();

		// Should stay on reset-password page due to validation
		await expect(page).toHaveURL('/reset-password');
	});

	test('shows form validation errors for mismatched passwords', async ({ page, goto }) => {
		await goto('/reset-password');
		await waitForPageLoad(page);

		await page.getByPlaceholder('Enter new password').fill('StrongPassword123!');
		await page.getByPlaceholder('Confirm new password').fill('DifferentPassword123!');
		await page.getByRole('button', { name: 'Update Password' }).click();

		// Should stay on reset-password page due to validation
		await expect(page).toHaveURL('/reset-password');
	});

	test('navigates to sign-in page from reset password page', async ({ page, goto }) => {
		await goto('/reset-password');
		await waitForPageLoad(page);

		await page.getByText('Back to Sign In').click();

		await expect(page).toHaveURL('/sign-in');
		await expect(page.getByText('Sign in to Foundation')).toBeVisible();
	});
});
