/**
 * Global teardown for E2E tests
 *
 * This file runs once after all tests complete. It handles cleanup of any
 * global resources that were set up during the test run.
 */

import { loadTestEnv } from '../../../server/utils/env-loader.js';

// Load test environment FIRST before importing database
loadTestEnv();

import { db } from '../../../server/utils/drizzle.js';
import { user } from '../../../server/db/schema/auth.js';
import { like, or } from 'drizzle-orm';

async function globalTeardown() {
	console.log('Global teardown - cleaning up after tests');

	try {
		// Clean up any remaining test users as a safety net
		// This catches any test users that weren't cleaned up due to test failures
		console.log('Checking for remaining test users...');

		const testUsers = await db
			.select({ id: user.id, email: user.email })
			.from(user)
			.where(or(like(user.email, '%test%'), like(user.email, '%example.com')));

		if (testUsers.length > 0) {
			console.log(`Found ${testUsers.length} remaining test users, cleaning up...`);

			// Delete test users (CASCADE DELETE will handle related records)
			await db.delete(user).where(or(like(user.email, '%test%'), like(user.email, '%example.com')));

			console.log(`✅ Cleaned up ${testUsers.length} test users`);
		} else {
			console.log('✅ No remaining test users found');
		}

		console.log('Global teardown completed successfully');
	} catch (error) {
		console.error('Global teardown failed:', error);
		// Don't throw here as we don't want to fail the test run during cleanup
	}
}

export default globalTeardown;
