# Testing Guide

This document provides information about how to run tests in this project, both locally and in CI.

## Overview

This project uses:

-   **Vitest** for unit testing
-   **Playwright** for end-to-end testing
-   **PostgreSQL** for the test database
-   **Drizzle ORM** for database access and seeding

## Test Structure

### Unit Tests

Unit tests are organized alongside the components they test:

```
frontend/components/
├── Button/
│   ├── Button.vue
│   └── Button.test.ts
├── Card/
│   ├── Card.vue
│   └── Card.test.ts
```

This co-location makes it easy to find tests and maintain them when components change.

### End-to-End Tests

End-to-end tests are located in the `frontend/tests/e2e` directory:

```
frontend/tests/
└── e2e/
    ├── index.spec.ts
    ├── auth.spec.ts
    └── ...
```

## End-to-End Tests

Our end-to-end tests use <PERSON><PERSON> with a focus on **smoke tests** - simple tests that verify pages render without errors.

### Philosophy

-   **Smoke Tests Only**: Tests verify pages load and render without JavaScript errors
-   **No Database Dependencies**: No database setup or user creation required
-   **Fast & Reliable**: Minimal setup for quick execution
-   **Page Accessibility Focus**: Verify basic page structure rather than complex functionality

### Running Tests

```bash
cd frontend && npm run test:e2e
```

No additional setup required - tests run against the application without external dependencies.

### GitHub Actions CI

Tests run automatically in CI with optimized settings. No additional secrets or database setup required for smoke tests.

## Test Structure

Our testing approach is organized into two main categories:

### Unit Tests

-   **Location**: Alongside components (`/components/ComponentName/ComponentName.test.ts`)
-   **Purpose**: Test individual components and functions in isolation
-   **Framework**: Vitest with Nuxt test utilities

### E2E Tests (Smoke Tests)

-   **Location**: `frontend/tests/e2e/`
-   **Purpose**: Verify pages render without errors
-   **Framework**: Playwright with Nuxt test utilities
-   **Approach**: Simple smoke tests with no database dependencies

## Running Different Types of Tests

-   **Unit Tests**: `npm run test`
-   **Unit Tests with Coverage**: `npm run test:coverage`
-   **Unit Tests with Coverage UI**: `npm run test:coverage:ui`
-   **E2E Tests**: `npm run test:e2e`
-   **E2E Tests with UI**: `npm run test:e2e:ui`
-   **E2E Tests in Headed Mode**: `npm run test:e2e:headed`
-   **E2E Tests in Debug Mode**: `npm run test:e2e:debug`

## Test Coverage

The project is configured to generate test coverage reports using Vitest's coverage tools. Coverage reports show how much of your code is covered by tests.

### Running Coverage Reports

```bash
# Generate coverage report
npm run test:coverage

# Generate coverage report with UI
npm run test:coverage:ui
```

### Coverage Configuration

Coverage is configured in `vitest.config.ts` with the following settings:

-   **Provider**: v8 (Node.js built-in coverage)
-   **Reporters**: text, JSON, and HTML
-   **Thresholds**:
    -   Lines: 70%
    -   Functions: 70%
    -   Branches: 60%
    -   Statements: 70%

### Coverage Exclusions

The following files and directories are excluded from coverage calculations:

-   **Test files**: `*.test.ts`, `*.spec.ts`, `tests/`
-   **Build and config files**: `node_modules/`, `dist/`, `.nuxt/`, `*.config.ts`, `nuxt.config.ts`, `playwright.config.ts`, `frontend/config/`
-   **Server and utility files**:
    -   `server/db/migrations/`
    -   `server/db/seed*.ts`
    -   `server/db/schema/`
    -   `server/plugins/`
    -   `server/utils/`
    -   `server/api/`
    -   `scripts/`
-   **Generated reports**: `playwright-report/`
-   **Auth schema**: `auth-schema.ts`
-   **Other non-testable files**: `*.d.ts`, `types/`, `mocks/`, `public/`, `assets/`, `lib/`

This ensures that coverage reports only include files that can and should be tested.

## Database Tools

-   **View Development Database**: `npm run db:studio`
-   **View Test Database**: `npm run db:studio:test`
    -   Note: The test database container must be running (`docker compose up db_test -d`)
-   **Generate Migrations**: `npm run db:generate`
-   **Apply Migrations to Development DB**: `npm run db:migrate`
-   **Apply Migrations to Test DB**: `npm run db:migrate:test`
    -   Note: This is usually not needed as migrations are applied automatically during tests
-   **Seed Development Database**: `npm run db:seed`
-   **Seed Test Database**: `npm run db:seed:test`
    -   Note: This will create test data in your test database for use with Drizzle Studio

## Schema Changes Workflow

When you make changes to your database schema:

1. Update schema files in `frontend/server/db/schema/`
2. Generate migrations: `npm run db:generate`
3. Apply migrations to development database: `npm run db:migrate`
4. Apply migrations to test database: `npm run db:migrate:test`
5. Start test database if not running: `docker compose up db_test -d`
6. Seed test database: `npm run db:seed:test`
7. View test database: `npm run db:studio:test`
8. Run tests to verify changes: `npm run test:e2e`

The migration scripts (`db:migrate` and `db:migrate:test`) use custom TypeScript scripts in the `frontend/scripts` directory that provide better error handling and environment management.

Note: When running tests with `npm run test:e2e`, migrations and seeding are handled automatically.
