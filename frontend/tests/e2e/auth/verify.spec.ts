import { test, expect } from '@playwright/test';
import { waitForPageLoad } from '../utils/helpers.js';

test.describe('Verify Page', () => {
test('renders verify page without errors', async ({ page }) => {
await page.goto('/verify');
await waitForPageLoad(page);

// Verify the page loaded successfully (smoke test)
await expect(page).toHaveTitle(/Foundation/);

// Check that basic page structure is present
await expect(page.locator('body')).toBeVisible();

// Verify we're on the correct page
await expect(page).toHaveURL('/verify');
});
});
