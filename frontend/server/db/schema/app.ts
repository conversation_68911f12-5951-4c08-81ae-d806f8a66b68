import { pgTable, text, timestamp } from 'drizzle-orm/pg-core';
import { pgEnum } from 'drizzle-orm/pg-core';
import { user } from './auth';

// Theme enum
export const themeEnum = pgEnum('theme', ['light', 'dark', 'system']);

// User settings table
export const userSettings = pgTable('user_settings', {
	id: text('id').primaryKey(),
	userId: text('userId')
		.notNull()
		.references(() => user.id, { onDelete: 'cascade' }),
	theme: themeEnum('theme').notNull().default('system'),
	createdAt: timestamp('createdAt').notNull(),
	updatedAt: timestamp('updatedAt').notNull(),
});
