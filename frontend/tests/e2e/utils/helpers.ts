/**
 * E2E test helpers
 *
 * This file contains helper functions for E2E tests to make them more maintainable
 * and consistent across the test suite.
 */
import type { Page } from '@playwright/test';

/**
 * Waits for the page to be fully loaded and hydrated
 * @param page Playwright page object
 */
export async function waitForPageLoad(page: Page): Promise<void> {
	// Wait for the page to be fully loaded
	await page.waitForLoadState('networkidle', {
		timeout: process.env.CI ? 60000 : 30000,
	});

	// Additional wait for any dynamic content
	await page.waitForTimeout(1000);
}
