# Test environment variables for smoke tests
# These are minimal variables needed for the app to start

# Disable database connections for smoke tests
DATABASE_URL=""

# Minimal auth config (not used in smoke tests but needed for app to start)
BETTER_AUTH_SECRET="test-secret-for-smoke-tests-only"
BETTER_AUTH_URL="http://localhost:3000"

# Disable external services for smoke tests
RESEND_API_KEY=""
SENTRY_DSN=""
GOOGLE_CLIENT_ID=""
GOOGLE_CLIENT_SECRET=""
TWITTER_CLIENT_ID=""
TWITTER_CLIENT_SECRET=""
