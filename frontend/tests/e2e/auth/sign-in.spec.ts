import { test, expect } from '@nuxt/test-utils/playwright';
import { waitForPageLoad } from '../utils/helpers.js';

test.describe('Sign-in Page', () => {
	test('renders sign-in page correctly', async ({ page, goto }) => {
		await goto('/sign-in');
		await waitForPageLoad(page);

		// Check page title and main elements
		await expect(page.getByText('Sign in to Foundation')).toBeVisible();
		await expect(page.getByPlaceholder('Email address')).toBeVisible();
		await expect(page.getByPlaceholder('Password')).toBeVisible();
		await expect(page.getByRole('button', { name: 'Sign In' })).toBeVisible();

		// Check social sign-in buttons
		await expect(page.getByRole('button', { name: 'Google' })).toBeVisible();
		await expect(page.getByRole('button', { name: 'Twitter' })).toBeVisible();
	});

	test('shows form validation errors for empty fields', async ({ page, goto }) => {
		await goto('/sign-in');
		await waitForPageLoad(page);

		// Try to submit empty form
		await page.getByRole('button', { name: 'Sign In' }).click();

		// Should stay on sign-in page (form validation prevents submission)
		await expect(page).toHaveURL('/sign-in');
	});

	test('shows form validation errors for invalid email', async ({ page, goto }) => {
		await goto('/sign-in');
		await waitForPageLoad(page);

		await page.getByPlaceholder('Email address').fill('invalid-email');
		await page.getByPlaceholder('Password').fill('somepassword');
		await page.getByRole('button', { name: 'Sign In' }).click();

		// Should stay on sign-in page due to validation
		await expect(page).toHaveURL('/sign-in');
	});

	test('navigates to sign-up page from sign-in page', async ({ page, goto }) => {
		await goto('/sign-in');
		await waitForPageLoad(page);

		await page.getByText('Sign up for an account').click();

		await expect(page).toHaveURL('/sign-up');
		await expect(page.getByText('Sign up to Foundation')).toBeVisible();
	});

	test('navigates to forgotten password page from sign-in page', async ({ page, goto }) => {
		await goto('/sign-in');
		await waitForPageLoad(page);

		await page.getByText('Forgotten password?').click();

		await expect(page).toHaveURL('/forgotten-password');
		await expect(page.getByText('Enter your email below to reset your password')).toBeVisible();
	});
});
