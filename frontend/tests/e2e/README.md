# E2E Testing Guide

This document provides an overview of the simplified E2E testing setup for the project.

## Overview

We use <PERSON><PERSON> for E2E testing with a focus on **smoke tests** - simple tests that verify pages render without errors. These tests are designed to be fast, reliable, and require minimal setup.

## Philosophy

Our E2E tests follow a "smoke test" approach:
- **Primary Goal**: Verify that pages load and render without JavaScript errors
- **No Database Dependencies**: Tests don't require database setup or user creation
- **No Complex Interactions**: Focus on page accessibility rather than detailed functionality
- **Fast Execution**: Minimal setup means faster test runs

## Setup

### Prerequisites

- Node.js 20+
- npm

### Local Development

Run the tests:
```bash
npm run test:e2e
```

### CI Environment

Tests run automatically as part of the GitHub Actions workflow with optimized settings for CI environments.

## Test Structure

- **Helpers**: `tests/e2e/utils/helpers.ts` - Common test helpers (page load waiting)
- **Test Files**: `tests/e2e/*.spec.ts` - Smoke test files organized by page/feature

## What We Test

Each test follows the same simple pattern:
1. Navigate to the page
2. Wait for page to load and hydrate
3. Verify the page title contains "Foundation"
4. Verify the page body is visible
5. Verify we're on the correct URL

### Current Test Coverage

- **Home Page** (`/`) - Basic landing page rendering
- **Authentication Pages**:
  - Sign-in page (`/sign-in`)
  - Sign-up page (`/sign-up`) 
  - Email verification page (`/verify`)
  - Forgotten password page (`/forgotten-password`)
  - Reset password page (`/reset-password`)

## Commands

- `npm run test:e2e` - Run all E2E tests using Chromium
- `npm run test:e2e:headed` - Run tests with visible browser
- `npm run test:e2e:debug` - Run tests in debug mode
- `npm run test:e2e:ui` - Run tests with Playwright UI
- `npm run test:e2e:ci` - Run tests with CI-optimized settings

## Adding New Tests

To add a new smoke test:

1. Create a new `.spec.ts` file in the appropriate directory
2. Follow the existing pattern:

```typescript
import { test, expect } from '@nuxt/test-utils/playwright';
import { waitForPageLoad } from '../utils/helpers.js';

test.describe('Your Page', () => {
	test('renders your page without errors', async ({ page, goto }) => {
		await goto('/your-route');
		await waitForPageLoad(page);

		// Verify the page loaded successfully (smoke test)
		await expect(page).toHaveTitle(/Foundation/);
		
		// Check that basic page structure is present
		await expect(page.locator('body')).toBeVisible();
		
		// Verify we're on the correct page
		await expect(page).toHaveURL('/your-route');
	});
});
```

## Troubleshooting

### Common Issues

1. **Timeout Errors**: The `waitForPageLoad` helper waits for Nuxt hydration - increase timeout if needed
2. **Title Mismatch**: Ensure your page sets an appropriate title containing "Foundation"
3. **URL Mismatch**: Check for redirects or middleware that might change the URL

### Debugging Tips

- Use `npm run test:e2e:headed` to see the browser
- Use `npm run test:e2e:debug` to step through tests
- Check browser console for JavaScript errors
