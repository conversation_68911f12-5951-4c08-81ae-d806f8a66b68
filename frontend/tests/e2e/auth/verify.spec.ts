import { test, expect } from '@nuxt/test-utils/playwright';
import { waitForPageLoad } from '../utils/helpers.js';

test.describe('Verify Page', () => {
test('renders verify page correctly when not signed in', async ({ page, goto }) => {
await goto('/verify');
await waitForPageLoad(page);

// Should show login prompt
await expect(page.getByText('Please Log In')).toBeVisible();
await expect(page.getByText('Log in to access your account and verify your email.')).toBeVisible();
await expect(page.getByRole('link', { name: 'Log In' })).toBeVisible();
});

test('shows error message for invalid token when not signed in', async ({ page, goto }) => {
await goto('/verify?error=invalid_token');
await waitForPageLoad(page);

await expect(page.getByText('There Was A Problem')).toBeVisible();
await expect(page.getByText('The verification link is invalid or expired. Please log in to resend the verification email.')).toBeVisible();
await expect(page.getByRole('link', { name: 'Log In' })).toBeVisible();
});

test('navigates to sign-in page when clicking Log In', async ({ page, goto }) => {
await goto('/verify');
await waitForPageLoad(page);

await page.getByRole('link', { name: 'Log In' }).click();
await expect(page).toHaveURL('/sign-in');
await expect(page.getByText('Sign in to Foundation')).toBeVisible();
});

test('handles different error states via URL parameters', async ({ page, goto }) => {
// Test expired token error
await goto('/verify?error=expired_token');
await waitForPageLoad(page);
await expect(page.getByText('There Was A Problem')).toBeVisible();

// Test already verified error
await goto('/verify?error=already_verified');
await waitForPageLoad(page);
await expect(page.getByText('There Was A Problem')).toBeVisible();
});
});
