#!/usr/bin/env tsx

/**
 * Manual cleanup script for test users
 * 
 * This script can be run manually to clean up test users from the database.
 * It's useful for cleaning up after failed test runs or for maintenance.
 * 
 * Usage:
 *   npm run cleanup:test-users
 *   or
 *   npx tsx scripts/cleanup-test-users.ts
 */

import { loadTestEnv } from '../server/utils/env-loader.js';

// Load test environment FIRST before importing database
loadTestEnv();

import { cleanupAllTestUsers } from '../tests/e2e/utils/test-users.js';

async function main() {
	console.log('🧹 Starting test user cleanup...\n');

	try {
		await cleanupAllTestUsers();
		console.log('\n✅ Test user cleanup completed successfully');
	} catch (error) {
		console.error('\n❌ Test user cleanup failed:', error);
		process.exit(1);
	}
}

// Run the cleanup
main();
