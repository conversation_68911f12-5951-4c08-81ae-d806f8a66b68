import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
	// Configure test directory
	testDir: './tests/e2e',

	// Configure test timeout - increased for CI environments
	timeout: process.env.CI ? 120 * 1000 : 60 * 1000,

	// Configure retries
	retries: process.env.CI ? 2 : 0,

	// Configure workers - use 1 in CI for stability, parallel in dev for speed
	workers: process.env.CI ? 1 : 1,

	// Configure browsers - only use Chromium
	projects: [
		{
			name: 'chromium',
			use: {
				...devices['Desktop Chrome'],
				// Base URL for all tests
				baseURL: 'http://localhost:3000',
				// Take screenshots on failure (only in local development)
				screenshot: process.env.CI ? 'off' : 'only-on-failure',
				// Record video on failure (only in local development)
				video: process.env.CI ? 'off' : 'retain-on-failure',
				// Record trace on failure (only in local development)
				trace: process.env.CI ? 'off' : 'retain-on-failure',
				// Add viewport configuration
				viewport: { width: 1280, height: 720 },
				// Add navigation timeout - increased for CI environments
				navigationTimeout: process.env.CI ? 60000 : 30000,
				// Add action timeout - increased for CI environments
				actionTimeout: process.env.CI ? 30000 : 10000,
				// Add test timeout
				testIdAttribute: 'data-testid',
			},
		},
	],

	// Start the web server for local development
	webServer: process.env.CI
		? undefined
		: {
				command: 'npm run dev',
				port: 3000,
				timeout: 120 * 1000,
				reuseExistingServer: true,
		  },

	// Configure reporter
	reporter: [['html'], ['list'], ['github']],
});
