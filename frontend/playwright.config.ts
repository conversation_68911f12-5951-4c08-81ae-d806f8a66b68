import { fileURLToPath } from 'node:url';
import { defineConfig, devices } from '@playwright/test';
import type { ConfigOptions } from '@nuxt/test-utils/playwright';

export default defineConfig<ConfigOptions>({
	// Use the Nuxt test utils integration
	use: {
		nuxt: {
			rootDir: fileURLToPath(new URL('.', import.meta.url)),
			// Build the app for testing
			build: true,
			// Run the app in preview mode (faster than dev mode)
			server: true,
		},
		// Base URL for all tests
		baseURL: 'http://localhost:3333',
		// Take screenshots on failure (only in local development)
		screenshot: process.env.CI ? 'off' : 'only-on-failure',
		// Record video on failure (only in local development)
		video: process.env.CI ? 'off' : 'retain-on-failure',
		// Record trace on failure (only in local development)
		trace: process.env.CI ? 'off' : 'retain-on-failure',
		// Add viewport configuration
		viewport: { width: 1280, height: 720 },
		// Add navigation timeout - increased for CI environments
		navigationTimeout: process.env.CI ? 60000 : 30000,
		// Add action timeout - increased for CI environments
		actionTimeout: process.env.CI ? 30000 : 10000,
		// Add test timeout
		testIdAttribute: 'data-testid',
	},

	// Configure test directory
	testDir: './tests/e2e',

	// Configure test timeout - increased for CI environments
	timeout: process.env.CI ? 120 * 1000 : 30 * 1000,

	// Configure retries
	retries: process.env.CI ? 2 : 0,

	// Configure workers - use 1 in CI for stability, parallel in dev for speed
	workers: process.env.CI ? 1 : '80%',

	// Configure browsers - only use Chromium
	projects: [
		{
			name: 'chromium',
			use: { ...devices['Desktop Chrome'] },
		},
	],

	// Only start the web server if we're not in CI
	...(process.env.CI
		? {}
		: {
				webServer: {
					command: 'NUXT_ENV_TEST=true nuxt dev --port 3333 --dotenv .env.test',
					port: 3333,
					timeout: 120 * 1000,
					reuseExistingServer: true,
				},
		  }),

	// Configure reporter
	reporter: [['html'], ['list'], ['github']],
});
